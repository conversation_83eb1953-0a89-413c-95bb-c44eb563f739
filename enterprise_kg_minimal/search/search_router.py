"""
Search Router for Intelligent Query Routing

This module combines query analysis and vector metadata analysis to make intelligent
routing decisions about whether to use semantic search only or include graph traversal.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .search_schemas import (
    SearchQuery,
    QueryComplexity,
    RoutingDecision,
    SearchStrategy,
    RoutingResult,
    VectorMetadata
)
from .query_analyzer import QueryAnalyzer
from .vector_metadata_analyzer import VectorMetadataAnalyzer

logger = logging.getLogger(__name__)


class SearchRouter:
    """
    Intelligent router that determines optimal search strategy based on query analysis
    and vector metadata sufficiency.
    """
    
    def __init__(self):
        """Initialize the search router with analyzers."""
        self.query_analyzer = QueryAnalyzer()
        self.metadata_analyzer = VectorMetadataAnalyzer()
        
        # Routing thresholds
        self.thresholds = {
            'semantic_only_confidence': 0.8,
            'metadata_sufficiency': 0.7,
            'complexity_threshold': 0.4,
            'similarity_threshold': 0.6
        }
        
        logger.info("Search router initialized with intelligent routing capabilities")
    
    def route_query(
        self,
        query_text: Optional[str],
        chunk_indices: List[str],
        vector_metadata: Optional[List[VectorMetadata]] = None,
        override_decision: Optional[RoutingDecision] = None
    ) -> RoutingResult:
        """
        Route a query to determine the optimal search strategy.
        
        Args:
            query_text: The query text to analyze
            chunk_indices: Chunk indices from vector search
            vector_metadata: Optional metadata from vector search (e.g., Pinecone)
            override_decision: Optional manual override of routing decision
            
        Returns:
            RoutingResult with recommended strategy and parameters
        """
        start_time = datetime.now()
        
        try:
            # Handle override
            if override_decision:
                return self._create_override_result(override_decision, query_text)
            
            # Create dummy metadata if not provided
            if not vector_metadata and chunk_indices:
                vector_metadata = self.metadata_analyzer.create_dummy_metadata(
                    chunk_indices, query_text
                )
                logger.info(f"Created dummy metadata for {len(chunk_indices)} chunks")
            
            # Analyze query
            query_analysis = self.query_analyzer.analyze_query(query_text, vector_metadata)
            
            # Analyze metadata sufficiency
            metadata_analysis = {}
            if vector_metadata:
                metadata_analysis = self.metadata_analyzer.analyze_metadata_sufficiency(
                    query_text or "", vector_metadata, self.thresholds['similarity_threshold']
                )
            
            # Make routing decision
            routing_decision = self._make_routing_decision(query_analysis, metadata_analysis)
            
            # Calculate execution parameters
            execution_params = self._calculate_execution_parameters(routing_decision, query_analysis)
            
            # Estimate performance
            performance_estimates = self._estimate_performance(routing_decision, len(chunk_indices))
            
            # Create result
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds() * 1000
            
            result = RoutingResult(
                decision=routing_decision['decision'],
                confidence=routing_decision['confidence'],
                query_analysis=query_analysis,
                should_skip_graph=routing_decision['decision'] == RoutingDecision.SEMANTIC_ONLY,
                max_expansion_depth=execution_params['max_depth'],
                suggested_strategy=execution_params['strategy'],
                estimated_time_ms=performance_estimates['time_ms'],
                estimated_cost=performance_estimates['cost'],
                reasoning=routing_decision['reasoning'],
                debug_info={
                    'routing_time_ms': processing_time,
                    'metadata_analysis': metadata_analysis,
                    'thresholds_used': self.thresholds,
                    'chunk_count': len(chunk_indices)
                }
            )
            
            logger.info(f"Query routed to {routing_decision['decision'].value} "
                       f"(confidence: {routing_decision['confidence']:.2f}, "
                       f"time: {processing_time:.1f}ms)")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in query routing: {e}")
            return self._create_error_result(str(e), query_text)
    
    def _make_routing_decision(
        self,
        query_analysis,
        metadata_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Make the core routing decision based on analyses.
        
        Returns:
            Dictionary with decision, confidence, and reasoning
        """
        reasoning_parts = []
        
        # Check if metadata analysis suggests sufficiency
        if metadata_analysis.get('is_sufficient', False):
            confidence = metadata_analysis.get('confidence', 0.0)
            if confidence > self.thresholds['semantic_only_confidence']:
                reasoning_parts.append(f"High metadata sufficiency (score: {metadata_analysis.get('sufficiency_score', 0):.2f})")
                if metadata_analysis.get('has_direct_answer', False):
                    reasoning_parts.append("direct answer found in vector results")
                return {
                    'decision': RoutingDecision.SEMANTIC_ONLY,
                    'confidence': confidence,
                    'reasoning': "; ".join(reasoning_parts)
                }
        
        # Use query analysis recommendation as base
        base_decision = query_analysis.recommended_decision
        base_confidence = query_analysis.confidence_score
        
        # Adjust based on metadata analysis
        if metadata_analysis:
            metadata_score = metadata_analysis.get('sufficiency_score', 0.0)
            
            # If metadata is very poor, upgrade to full graph
            if metadata_score < 0.3 and base_decision == RoutingDecision.LIGHT_GRAPH:
                reasoning_parts.append("Low metadata quality, upgrading to full graph")
                return {
                    'decision': RoutingDecision.FULL_GRAPH,
                    'confidence': max(0.6, base_confidence),
                    'reasoning': "; ".join(reasoning_parts)
                }
            
            # If metadata is decent but not sufficient, consider light graph
            if 0.4 <= metadata_score < 0.7 and base_decision == RoutingDecision.FULL_GRAPH:
                if query_analysis.query_complexity != QueryComplexity.COMPLEX:
                    reasoning_parts.append("Moderate metadata quality, using light graph expansion")
                    return {
                        'decision': RoutingDecision.LIGHT_GRAPH,
                        'confidence': base_confidence * 0.9,
                        'reasoning': "; ".join(reasoning_parts)
                    }
        
        # Use original query analysis recommendation
        reasoning_parts.append(f"Following query analysis recommendation: {query_analysis.reasoning}")
        return {
            'decision': base_decision,
            'confidence': base_confidence,
            'reasoning': "; ".join(reasoning_parts)
        }
    
    def _calculate_execution_parameters(
        self,
        routing_decision: Dict[str, Any],
        query_analysis
    ) -> Dict[str, Any]:
        """Calculate execution parameters based on routing decision."""
        decision = routing_decision['decision']
        
        if decision == RoutingDecision.SEMANTIC_ONLY:
            return {
                'max_depth': 0,
                'strategy': SearchStrategy.SEMANTIC_ONLY
            }
        elif decision == RoutingDecision.LIGHT_GRAPH:
            return {
                'max_depth': 1,
                'strategy': SearchStrategy.CHUNK_EXPANSION
            }
        elif decision == RoutingDecision.FULL_GRAPH:
            # Choose strategy based on query characteristics
            if query_analysis.is_hierarchical_query:
                strategy = SearchStrategy.HIERARCHICAL
                max_depth = 3
            elif query_analysis.is_relational_query:
                strategy = SearchStrategy.RELATIONSHIP_CENTRIC
                max_depth = 2
            elif query_analysis.has_entity_references:
                strategy = SearchStrategy.ENTITY_CENTRIC
                max_depth = 2
            else:
                strategy = SearchStrategy.HYBRID
                max_depth = 2
            
            return {
                'max_depth': max_depth,
                'strategy': strategy
            }
        else:  # ADAPTIVE
            return {
                'max_depth': 2,
                'strategy': SearchStrategy.HYBRID
            }
    
    def _estimate_performance(self, routing_decision: Dict[str, Any], chunk_count: int) -> Dict[str, float]:
        """Estimate performance metrics for the routing decision."""
        decision = routing_decision['decision']
        
        # Base estimates (in milliseconds and arbitrary cost units)
        if decision == RoutingDecision.SEMANTIC_ONLY:
            base_time = 50
            base_cost = 1.0
        elif decision == RoutingDecision.LIGHT_GRAPH:
            base_time = 200
            base_cost = 3.0
        else:  # FULL_GRAPH or ADAPTIVE
            base_time = 500
            base_cost = 8.0
        
        # Scale with chunk count
        time_multiplier = 1 + (chunk_count * 0.1)
        cost_multiplier = 1 + (chunk_count * 0.05)
        
        return {
            'time_ms': base_time * time_multiplier,
            'cost': base_cost * cost_multiplier
        }
    
    def _create_override_result(self, override_decision: RoutingDecision, query_text: Optional[str]) -> RoutingResult:
        """Create result for manual override."""
        # Create minimal query analysis
        query_analysis = self.query_analyzer.analyze_query(query_text)
        
        # Set parameters based on override
        if override_decision == RoutingDecision.SEMANTIC_ONLY:
            max_depth = 0
            strategy = SearchStrategy.SEMANTIC_ONLY
        elif override_decision == RoutingDecision.LIGHT_GRAPH:
            max_depth = 1
            strategy = SearchStrategy.CHUNK_EXPANSION
        else:
            max_depth = 2
            strategy = SearchStrategy.HYBRID
        
        return RoutingResult(
            decision=override_decision,
            confidence=1.0,  # High confidence for manual override
            query_analysis=query_analysis,
            should_skip_graph=override_decision == RoutingDecision.SEMANTIC_ONLY,
            max_expansion_depth=max_depth,
            suggested_strategy=strategy,
            estimated_time_ms=100.0,
            estimated_cost=1.0,
            reasoning="Manual override specified",
            debug_info={'override': True}
        )
    
    def _create_error_result(self, error: str, query_text: Optional[str]) -> RoutingResult:
        """Create result for routing errors."""
        # Create minimal query analysis
        query_analysis = self.query_analyzer.analyze_query(query_text)
        
        return RoutingResult(
            decision=RoutingDecision.ADAPTIVE,
            confidence=0.5,
            query_analysis=query_analysis,
            should_skip_graph=False,
            max_expansion_depth=2,
            suggested_strategy=SearchStrategy.HYBRID,
            estimated_time_ms=500.0,
            estimated_cost=5.0,
            reasoning=f"Error in routing: {error}",
            debug_info={'error': error}
        )
    
    def update_thresholds(self, new_thresholds: Dict[str, float]) -> None:
        """Update routing thresholds for fine-tuning."""
        self.thresholds.update(new_thresholds)
        logger.info(f"Updated routing thresholds: {self.thresholds}")
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """Get statistics about routing decisions (placeholder for future implementation)."""
        return {
            'thresholds': self.thresholds,
            'analyzer_status': {
                'query_analyzer': 'active',
                'metadata_analyzer': 'active'
            }
        }
